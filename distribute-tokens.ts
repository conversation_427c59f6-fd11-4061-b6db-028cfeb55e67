/** biome-ignore-all lint/style/noNonNullAssertion: <explanation> */
import { ERC20TokenDistributor, type DistributionConfig } from './erc20-distribution'

// Configuration for token distribution
const config: DistributionConfig = {
  // ERC20 token contract address (replace with your token address)
  erc20TokenAddress: Bun.env.ERC20_TOKEN_ADDRESS! , // Example: USDC on Base
  
  // Percentage of totalUsageMax to distribute as tokens (e.g., 10 = 10%)
  distributionPercentage: Number(Bun.env.DISTRIBUTION_PERCENTAGE!),
  
  // Private key of the wallet that will distribute tokens (should have sufficient token balance)
  // ⚠️ NEVER commit this to version control! Use environment variables in production
  privateKey: Bun.env.DISTRIBUTOR_PRIVATE_KEY!,
  
  // Path to the CSV file containing user data
  csvFilePath: '/home/<USER>/Documents/augment-projects/vest-subs/nft-usage-results.csv',
  
  // Set to true for testing without sending real transactions
  dryRun: false
}

async function main() {
  console.log('🎯 ERC20 Token Distribution Script')
  console.log('=' .repeat(50))
  
  // Validate configuration
  if (config.privateKey === '0x0000000000000000000000000000000000000000000000000000000000000000') {
    console.error('❌ Please set DISTRIBUTOR_PRIVATE_KEY environment variable')
    process.exit(1)
  }
  
  try {
    const distributor = new ERC20TokenDistributor(config)
    
    // Get distribution statistics first
    console.log('📊 Calculating distribution statistics...')
    const stats = await distributor.getDistributionStats()
    console.log("🚀 ~ main ~ stats:", stats)
    
    console.log('\n📈 DISTRIBUTION STATISTICS')
    console.log('=' .repeat(50))
    console.log(`👥 Total recipients: ${stats.totalRecipients}`)
    console.log(`💸 Total tokens to distribute: ${stats.totalTokensToDistribute.toString()}`)
    console.log(`💰 Average per user: ${stats.averagePerUser.toString()}`)
    console.log(`🏦 Distributor balance: ${stats.distributorBalance.toString()}`)
    console.log(`✅ Can distribute: ${stats.canDistribute ? 'Yes' : 'No'}`)
    
    if (!stats.canDistribute) {
      console.error('❌ Insufficient balance to complete distribution')
      process.exit(1)
    }
    
    // Execute distribution
    await distributor.distributeTokens()
    
  } catch (error) {
    console.error('💥 Error:', error)
    process.exit(1)
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Distribution cancelled by user')
  process.exit(0)
})

// Run the distribution
main().catch(console.error)