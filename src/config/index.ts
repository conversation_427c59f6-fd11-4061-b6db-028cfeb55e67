import { existsSync } from 'fs'

/**
 * Centralized configuration module for the ERC20 Distribution API
 * Handles environment variable loading, validation, and provides typed configuration
 */

// Load environment variables from .env files
const envFiles = ['.env.local', '.env']
for (const envFile of envFiles) {
  if (existsSync(envFile)) {
    console.log(`📄 Loading environment from ${envFile}`)
    break
  }
}

// Configuration interface
export interface AppConfig {
  // Server configuration
  port: number
  enableCron: boolean
  cronSchedule: string
  
  // Blockchain configuration
  distributorPrivateKey: string
  erc20TokenAddress: string
  distributionPercentage: number
  
  // File paths
  csvFilePath: string
  
  // Environment validation
  isValid: boolean
  missingVars: string[]
}

// Environment variable getters with fallbacks
function getEnvVar(key: string): string | undefined {
  return Bun.env[key] || process.env[key]
}

function getEnvVarWithDefault(key: string, defaultValue: string): string {
  return getEnvVar(key) || defaultValue
}

function getEnvNumberWithDefault(key: string, defaultValue: number): number {
  const value = getEnvVar(key)
  return value ? Number(value) : defaultValue
}

function getEnvBooleanWithDefault(key: string, defaultValue: boolean): boolean {
  const value = getEnvVar(key)
  return value ? value === 'true' : defaultValue
}

// Validate required environment variables
export function validateEnvironment(): { valid: boolean; missing: string[] } {
  const required = ['DISTRIBUTOR_PRIVATE_KEY']
  const missing = required.filter(key => !getEnvVar(key))
  
  return {
    valid: missing.length === 0,
    missing
  }
}

// Validate private key format
function validatePrivateKey(): void {
  const privateKey = getEnvVar('DISTRIBUTOR_PRIVATE_KEY')
  
  if (!privateKey) {
    throw new Error('DISTRIBUTOR_PRIVATE_KEY is required')
  }
  
  if (!privateKey.startsWith('0x') || privateKey.length !== 66) {
    throw new Error('DISTRIBUTOR_PRIVATE_KEY must be a valid hex string starting with 0x (66 characters total)')
  }
}

// Create and export configuration
function createConfig(): AppConfig {
  const envValidation = validateEnvironment()
  
  return {
    // Server configuration
    port: getEnvNumberWithDefault('PORT', 3000),
    enableCron: getEnvBooleanWithDefault('ENABLE_CRON', true),
    cronSchedule: getEnvVarWithDefault('CRON_SCHEDULE', '0 0 * * *'), // Every midnight
    
    // Blockchain configuration
    distributorPrivateKey: getEnvVar('DISTRIBUTOR_PRIVATE_KEY') || '',
    erc20TokenAddress: getEnvVarWithDefault('ERC20_TOKEN_ADDRESS', '0x481FE356DF88169f5F38203Dd7f3C67B7559FDa5'),
    distributionPercentage: getEnvNumberWithDefault('DISTRIBUTION_PERCENTAGE', 0),
    
    // File paths
    csvFilePath: getEnvVarWithDefault('CSV_FILE_PATH', '/home/<USER>/Documents/augment-projects/vest-subs/nft-usage-results.csv'),
    
    // Environment validation
    isValid: envValidation.valid,
    missingVars: envValidation.missing
  }
}

// Export the configuration instance
export const config = createConfig()

// Export validation functions for use in startup
export { validateEnvironment as validateEnv, validatePrivateKey }

// Helper function to log configuration (without sensitive data)
export function logConfiguration(): void {
  console.log(`📊 Configuration:`)
  console.log(`   - Port: ${config.port}`)
  console.log(`   - Token Address: ${config.erc20TokenAddress}`)
  console.log(`   - Distribution %: ${config.distributionPercentage}%`)
  console.log(`   - CSV Path: ${config.csvFilePath}`)
  console.log(`   - Cron Enabled: ${config.enableCron}`)
  console.log(`   - Cron Schedule: ${config.cronSchedule}`)
  console.log(`   - Private Key: ${config.distributorPrivateKey ? '✅ Set' : '❌ Missing'}`)
  console.log(`   - Environment Valid: ${config.isValid ? '✅ Valid' : '❌ Invalid'}`)
  
  if (!config.isValid) {
    console.log(`   - Missing Variables: ${config.missingVars.join(', ')}`)
  }
}
