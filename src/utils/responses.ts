import type { ApiResponse } from '../types'

/**
 * Utility functions for creating standardized API responses
 */

// Create a standardized API response
export function createResponse<T>(success: boolean, data?: T, error?: string): ApiResponse<T> {
  return {
    success,
    data,
    error,
    timestamp: new Date().toISOString()
  }
}

// Create a success response
export function createSuccessResponse<T>(data: T): ApiResponse<T> {
  return createResponse(true, data)
}

// Create an error response
export function createErrorResponse(error: string): ApiResponse<null> {
  return createResponse(false, null, error)
}

// Helper function to get next cron run time
export function getNextCronRun(): string {
  // Simple calculation for next midnight
  const now = new Date()
  const tomorrow = new Date(now)
  tomorrow.setDate(tomorrow.getDate() + 1)
  tomorrow.setHours(0, 0, 0, 0)
  return tomorrow.toISOString()
}
