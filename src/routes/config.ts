import { Hono } from 'hono'
import { createSuccessResponse, createErrorResponse } from '../utils/responses'
import { config, validateEnv } from '../config'
import type { ConfigUpdateRequest } from '../types'

/**
 * Configuration management routes
 */

const configRoutes = new Hono()

// Get distribution configuration
configRoutes.get('/api/config', (c) => {
  const envCheck = validateEnv()
  
  return c.json(createSuccessResponse({
    erc20TokenAddress: config.erc20TokenAddress,
    distributionPercentage: config.distributionPercentage,
    csvFilePath: config.csvFilePath,
    cronSchedule: config.cronSchedule,
    cronEnabled: config.enableCron,
    environmentValid: envCheck.valid,
    missingEnvVars: envCheck.missing
  }))
})

// Update configuration
configRoutes.put('/api/config', async (c) => {
  try {
    const body = await c.req.json() as ConfigUpdateRequest
    
    // Validate the request body
    if (typeof body !== 'object' || body === null) {
      return c.json(createErrorResponse('Invalid request body'), 400)
    }
    
    // For now, we'll just return the current config since we can't modify env vars at runtime
    // In a real application, you might want to store these in a database
    return c.json(createSuccessResponse({
      message: 'Configuration update received',
      note: 'Environment variables cannot be modified at runtime. Restart the server with new .env values.',
      received: body,
      current: {
        erc20TokenAddress: config.erc20TokenAddress,
        distributionPercentage: config.distributionPercentage,
        csvFilePath: config.csvFilePath,
        cronSchedule: config.cronSchedule,
        cronEnabled: config.enableCron
      }
    }))
  } catch (error) {
    console.error('Error updating configuration:', error)
    return c.json(createErrorResponse(error instanceof Error ? error.message : 'Unknown error'), 500)
  }
})

export { configRoutes }
