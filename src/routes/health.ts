import { Hono } from 'hono'
import { createSuccessResponse } from '../utils/responses'
import { config } from '../config'

/**
 * Health check routes
 */

const health = new Hono()

// Health check endpoint
health.get('/health', (c) => {
  return c.json(createSuccessResponse({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: {
      nodeVersion: process.version,
      platform: process.platform,
      configValid: config.isValid
    }
  }))
})

export { health }
