import { cors } from 'hono/cors'
import { logger } from 'hono/logger'
import { prettyJSON } from 'hono/pretty-json'
import type { MiddlewareHandler } from 'hono'

/**
 * Centralized middleware configuration for the Hono application
 */

// CORS middleware configuration
export const corsMiddleware = cors({
  origin: ['http://localhost:3000', 'http://localhost:5173'], // Add your frontend URLs
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
})

// Logger middleware
export const loggerMiddleware = logger()

// Pretty JSON middleware
export const prettyJSONMiddleware = prettyJSON()

// Error handling middleware
export const errorHandler: MiddlewareHandler = async (c, next) => {
  try {
    await next()
  } catch (error) {
    console.error('Unhandled error:', error)
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    
    return c.json({
      success: false,
      error: errorMessage,
      timestamp: new Date().toISOString()
    }, 500)
  }
}

// Export all middleware as an array for easy application
export const allMiddleware = [
  loggerMiddleware,
  corsMiddleware,
  prettyJSONMiddleware,
  errorHandler
]
