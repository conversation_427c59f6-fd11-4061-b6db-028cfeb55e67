/**
 * Shared type definitions for the ERC20 Distribution API
 */

// Standard API response format
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  timestamp: string
}

// Distribution status tracking
export interface DistributionStatus {
  isRunning: boolean
  lastRun?: string
  nextRun?: string
  lastResult?: {
    success: boolean
    recipientCount: number
    totalAmount: string
    error?: string
  }
}

// Distribution statistics
export interface DistributionStats {
  totalRecipients: number
  totalTokensToDistribute: bigint
  averagePerUser: bigint
  distributorBalance: bigint
  canDistribute: boolean
}

// Configuration update request
export interface ConfigUpdateRequest {
  distributionPercentage?: number
  cronSchedule?: string
  enableCron?: boolean
  csvFilePath?: string
}

// Environment validation result
export interface EnvironmentValidation {
  valid: boolean
  missing: string[]
}

// Re-export types from erc20-distribution for convenience
export type { DistributionConfig } from '../../erc20-distribution'
